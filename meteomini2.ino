/**
 * @file meteomini2.ino
 * @brief Professional IoT Weather Station for ESP32-C3
 * @version 2.0.0
 * <AUTHOR> Agent (Professional Refactor)
 * 
 * Features:
 * - Multi-sensor support (SHT40, BME280, SCD41, DS18B20)
 * - Dual server support (TMEP.cz, ThingSpeak)
 * - WiFi configuration portal with LED indication
 * - Deep sleep power management
 * - Battery monitoring with emergency modes
 * - Non-blocking operations
 * - Robust error handling
 * - Clean architecture with separation of concerns
 */

#include <WiFi.h>
#include <WiFiManager.h>
#include <HTTPClient.h>
#include <EEPROM.h>
#include <Wire.h>
#include <SensirionI2CSht4x.h>
#include <Adafruit_BME280.h>
#include <SensirionI2CScd4x.h>
#include <OneWire.h>
#include <DallasTemperature.h>

// ============================================================================
// CONSTANTS AND CONFIGURATION
// ============================================================================

// Hardware Configuration
constexpr uint8_t LED_PIN = 7;
constexpr uint8_t TRIGGER_PIN = 9;
constexpr uint8_t PWR_PIN = 8;
constexpr uint8_t ONEWIRE_PIN = 10;
constexpr uint8_t DEFAULT_SDA_PIN = 4;
constexpr uint8_t DEFAULT_SCL_PIN = 5;

// Power Management
constexpr float BATTERY_LOW_THRESHOLD = 3.0f;
constexpr float EXTERNAL_POWER_THRESHOLD = 4.5f;
constexpr uint16_t EMERGENCY_SLEEP_TIME = 60; // minutes
constexpr uint8_t MAX_WIFI_CONNECT_ATTEMPTS = 5;

// Timing Configuration
constexpr uint32_t LED_BLINK_INTERVAL = 500;
constexpr uint32_t PORTAL_AUTO_CLOSE_TIME = 300000; // 5 minutes
constexpr uint16_t CONFIG_TIMEOUT_BATTERY = 120;    // 2 minutes
constexpr uint16_t CONFIG_TIMEOUT_EXTERNAL = 300;   // 5 minutes

// EEPROM Configuration
constexpr uint16_t EEPROM_SIZE = 512;
constexpr uint8_t CONFIG_VERSION = 2;

// Sensor Types
enum class SensorType : uint8_t {
    SHT40_0x44 = 0,
    SHT40_0x45 = 1,
    BME280_0x76 = 2,
    BME280_0x77 = 3,
    SCD41 = 4,
    DS18B20 = 5,
    AUTO_DETECT = 99
};

// Server Types
enum class ServerType : uint8_t {
    NONE = 0,
    TMEP = 1,
    THINGSPEAK = 2,
    BOTH = 3
};

// ============================================================================
// CONFIGURATION STRUCTURE
// ============================================================================

struct Config {
    uint8_t configVersion = CONFIG_VERSION;
    char serverAddressTmep[40] = "xny3ef-8khshy.tmep.cz/index.php";
    char thingSpeakApiKey[40] = "ABS8T4OULVYA1FOL";
    char thingSpeakChannelId[20] = "845449";
    uint16_t sleepTime = 5;
    SensorType sensorType = SensorType::AUTO_DETECT;
    ServerType serverSendType = ServerType::BOTH;
    uint8_t sdaPin = DEFAULT_SDA_PIN;
    uint8_t sclPin = DEFAULT_SCL_PIN;
    uint8_t tsFieldTemp = 2;
    uint8_t tsFieldHum = 3;
    uint8_t tsFieldPress = 5;
    uint8_t tsFieldCO2 = 6;
    uint8_t tsFieldBatt = 4;
    
    bool isValid() const {
        return configVersion == CONFIG_VERSION &&
               sleepTime > 0 && sleepTime <= 200 &&
               static_cast<uint8_t>(sensorType) <= 99 &&
               static_cast<uint8_t>(serverSendType) <= 3;
    }
    
    void setDefaults() {
        *this = Config{}; // Reset to default values
    }
};

// ============================================================================
// GLOBAL VARIABLES
// ============================================================================

// Configuration
Config config;

// Hardware Objects
WiFiManager wifiManager;
SensirionI2CSht4x sht4x;
Adafruit_BME280 bme;
SensirionI2CScd4x scd4x;
OneWire oneWire(ONEWIRE_PIN);
DallasTemperature ds18b20(&oneWire);

// State Variables
RTC_DATA_ATTR uint32_t bootCount = 0;
RTC_DATA_ATTR uint8_t failedConnectAttempts = 0;

// Portal State
volatile bool portalRunning = false;
volatile bool ledState = false;
uint32_t lastLedBlink = 0;
uint32_t portalStartTime = 0;

// Sensor Data
DeviceAddress sensorAddress;
const char* version = "2.0.0";

// ============================================================================
// FORWARD DECLARATIONS
// ============================================================================

class PowerManager;
class SensorManager;
class ConfigManager;
class NetworkManager;
class LEDManager;

// ============================================================================
// POWER MANAGEMENT CLASS
// ============================================================================

class PowerManager {
public:
    static float getBatteryVoltage() {
        constexpr uint8_t SAMPLES = 5;
        uint32_t sum = 0;
        
        for (uint8_t i = 0; i < SAMPLES; i++) {
            sum += analogRead(A0);
            delay(10);
        }
        
        float voltage = (sum / SAMPLES) * (3.3f / 4095.0f) * 2.0f;
        return voltage;
    }
    
    static bool isRunningOnBattery() {
        return getBatteryVoltage() < EXTERNAL_POWER_THRESHOLD;
    }
    
    static bool isBatteryLow() {
        return getBatteryVoltage() < BATTERY_LOW_THRESHOLD;
    }
    
    static void enterEmergencyMode() {
        Serial.printf("EMERGENCY: Low battery (%.2fV < %.2fV)!\n",
                     getBatteryVoltage(), BATTERY_LOW_THRESHOLD);
        Serial.printf("Entering emergency sleep for %d minutes.\n", EMERGENCY_SLEEP_TIME);
        
        esp_sleep_enable_timer_wakeup(EMERGENCY_SLEEP_TIME * 60ULL * 1000000ULL);
        esp_deep_sleep_start();
    }
    
    static void enterSleep(uint16_t minutes) {
        Serial.printf("Entering sleep for %d minutes\n", minutes);
        
        // Power down sensors
        digitalWrite(PWR_PIN, LOW);
        
        // Configure wakeup
        esp_sleep_enable_timer_wakeup(minutes * 60ULL * 1000000ULL);
        esp_sleep_enable_ext0_wakeup(static_cast<gpio_num_t>(TRIGGER_PIN), 0);
        
        esp_deep_sleep_start();
    }
};

// ============================================================================
// LED MANAGER CLASS
// ============================================================================

class LEDManager {
public:
    static void init() {
        pinMode(LED_PIN, OUTPUT);
        digitalWrite(LED_PIN, LOW);
    }
    
    static void startBlinking() {
        digitalWrite(LED_PIN, HIGH);
        ledState = true;
        lastLedBlink = millis();
        portalStartTime = millis();
        Serial.println("LED blinking started");
    }
    
    static void stopBlinking() {
        digitalWrite(LED_PIN, LOW);
        ledState = false;
        Serial.println("LED blinking stopped");
    }
    
    static void update() {
        if (portalRunning && (millis() - lastLedBlink >= LED_BLINK_INTERVAL)) {
            ledState = !ledState;
            digitalWrite(LED_PIN, ledState ? HIGH : LOW);
            lastLedBlink = millis();
        }
    }
};

// ============================================================================
// CONFIGURATION MANAGER CLASS
// ============================================================================

class ConfigManager {
public:
    static bool save() {
        EEPROM.put(0, config);
        bool success = EEPROM.commit();
        if (success) {
            Serial.println("Configuration saved to EEPROM");
        } else {
            Serial.println("Failed to save configuration");
        }
        return success;
    }
    
    static bool load() {
        Config tempConfig;
        EEPROM.get(0, tempConfig);
        
        if (tempConfig.isValid()) {
            config = tempConfig;
            Serial.println("Configuration loaded from EEPROM");
            return true;
        } else {
            Serial.println("Invalid configuration, using defaults");
            config.setDefaults();
            save();
            return false;
        }
    }
    
    static void reset() {
        config.setDefaults();
        save();
        Serial.println("Configuration reset to defaults");
    }
    
    static void printConfig() {
        Serial.println("=== Current Configuration ===");
        Serial.printf("TMEP Server: %s\n", config.serverAddressTmep);
        Serial.printf("ThingSpeak Channel: %s\n", config.thingSpeakChannelId);
        Serial.printf("Sleep Time: %d minutes\n", config.sleepTime);
        Serial.printf("Sensor Type: %d\n", static_cast<uint8_t>(config.sensorType));
        Serial.printf("Server Type: %d\n", static_cast<uint8_t>(config.serverSendType));
        Serial.printf("I2C Pins: SDA=%d, SCL=%d\n", config.sdaPin, config.sclPin);
        Serial.println("=============================");
    }
};

// ============================================================================
// SENSOR MANAGER CLASS
// ============================================================================

class SensorManager {
public:
    struct SensorData {
        float temperature = 0.0f;
        float humidity = 0.0f;
        float pressure = 0.0f;
        uint16_t co2 = 0;
        bool isValid = false;

        bool validateData() {
            // Temperature validation
            if (isnan(temperature) || temperature < -40.0f || temperature > 85.0f) {
                Serial.printf("Invalid temperature: %.2f\n", temperature);
                return false;
            }

            // Humidity validation (for sensors that support it)
            if (static_cast<uint8_t>(config.sensorType) <= 4) {
                if (isnan(humidity) || humidity < 0.0f || humidity > 100.0f) {
                    Serial.printf("Invalid humidity: %.2f\n", humidity);
                    return false;
                }
            }

            // Pressure validation (BME280 only)
            if (static_cast<uint8_t>(config.sensorType) == 2 || static_cast<uint8_t>(config.sensorType) == 3) {
                if (isnan(pressure) || pressure < 300.0f || pressure > 1100.0f) {
                    Serial.printf("Invalid pressure: %.2f\n", pressure);
                    return false;
                }
            }

            // CO2 validation (SCD41 only)
            if (config.sensorType == SensorType::SCD41) {
                if (co2 < 400 || co2 > 5000) {
                    Serial.printf("Invalid CO2: %d\n", co2);
                    return false;
                }
            }

            return true;
        }
    };

    static bool initializeSensors() {
        Serial.println("Initializing sensors...");

        // Power up sensors
        pinMode(PWR_PIN, OUTPUT);
        digitalWrite(PWR_PIN, HIGH);
        delay(100);

        // Initialize I2C
        Wire.begin(config.sdaPin, config.sclPin);

        // Initialize OneWire
        ds18b20.begin();

        // Try to detect sensors
        if (config.sensorType == SensorType::AUTO_DETECT) {
            return autoDetectSensor();
        } else {
            return initializeSpecificSensor(config.sensorType);
        }
    }

    static bool autoDetectSensor() {
        Serial.println("Auto-detecting sensors...");

        // Try SHT40 0x44
        if (initializeSpecificSensor(SensorType::SHT40_0x44)) {
            config.sensorType = SensorType::SHT40_0x44;
            return true;
        }

        // Try SHT40 0x45
        if (initializeSpecificSensor(SensorType::SHT40_0x45)) {
            config.sensorType = SensorType::SHT40_0x45;
            return true;
        }

        // Try BME280 0x76
        if (initializeSpecificSensor(SensorType::BME280_0x76)) {
            config.sensorType = SensorType::BME280_0x76;
            return true;
        }

        // Try BME280 0x77
        if (initializeSpecificSensor(SensorType::BME280_0x77)) {
            config.sensorType = SensorType::BME280_0x77;
            return true;
        }

        // Try SCD41
        if (initializeSpecificSensor(SensorType::SCD41)) {
            config.sensorType = SensorType::SCD41;
            return true;
        }

        // Try DS18B20
        if (initializeSpecificSensor(SensorType::DS18B20)) {
            config.sensorType = SensorType::DS18B20;
            return true;
        }

        Serial.println("WARNING: No sensor detected!");
        return false;
    }

    static bool initializeSpecificSensor(SensorType type) {
        switch (type) {
            case SensorType::SHT40_0x44:
                return initSHT40(0x44);
            case SensorType::SHT40_0x45:
                return initSHT40(0x45);
            case SensorType::BME280_0x76:
                return initBME280(0x76);
            case SensorType::BME280_0x77:
                return initBME280(0x77);
            case SensorType::SCD41:
                return initSCD41();
            case SensorType::DS18B20:
                return initDS18B20();
            default:
                return false;
        }
    }

    static SensorData readSensorData() {
        SensorData data;

        switch (config.sensorType) {
            case SensorType::SHT40_0x44:
            case SensorType::SHT40_0x45:
                data.isValid = readSHT40(data);
                break;
            case SensorType::BME280_0x76:
            case SensorType::BME280_0x77:
                data.isValid = readBME280(data);
                break;
            case SensorType::SCD41:
                data.isValid = readSCD41(data);
                break;
            case SensorType::DS18B20:
                data.isValid = readDS18B20(data);
                break;
            default:
                data.isValid = false;
        }

        if (data.isValid) {
            data.isValid = data.validateData();
        }

        return data;
    }

private:
    static bool initSHT40(uint8_t address) {
        sht4x.begin(Wire, address);
        uint32_t serialNumber;
        uint16_t error = sht4x.serialNumber(serialNumber);

        if (error == 0) {
            Serial.printf("SHT40 sensor found at 0x%02X\n", address);
            return true;
        }
        return false;
    }

    static bool initBME280(uint8_t address) {
        if (bme.begin(address)) {
            Serial.printf("BME280 sensor found at 0x%02X\n", address);
            bme.setSampling(Adafruit_BME280::MODE_FORCED,
                           Adafruit_BME280::SAMPLING_X1,
                           Adafruit_BME280::SAMPLING_X1,
                           Adafruit_BME280::SAMPLING_X1,
                           Adafruit_BME280::FILTER_OFF);
            return true;
        }
        return false;
    }

    static bool initSCD41() {
        scd4x.begin(Wire, 0x62);
        uint16_t error = scd4x.startPeriodicMeasurement();

        if (error == 0) {
            Serial.println("SCD41 sensor found");
            delay(200); // Warm-up time
            return true;
        }
        return false;
    }

    static bool initDS18B20() {
        if (oneWire.search(sensorAddress)) {
            Serial.println("DS18B20 sensor found");
            return true;
        }
        oneWire.reset_search();
        return false;
    }

    static bool readSHT40(SensorData& data) {
        uint16_t error = sht4x.measureHighPrecision(data.temperature, data.humidity);
        if (error != 0) {
            Serial.println("Error reading SHT40 sensor");
            return false;
        }
        return true;
    }

    static bool readBME280(SensorData& data) {
        bme.takeForcedMeasurement();
        data.temperature = bme.readTemperature();
        data.humidity = bme.readHumidity();
        data.pressure = bme.readPressure() / 100.0f;
        return true;
    }

    static bool readSCD41(SensorData& data) {
        uint16_t error = scd4x.readMeasurement(data.co2, data.temperature, data.humidity);
        if (error != 0) {
            Serial.println("Error reading SCD41 sensor");
            return false;
        }
        return true;
    }

    static bool readDS18B20(SensorData& data) {
        ds18b20.requestTemperatures();
        data.temperature = ds18b20.getTempC(sensorAddress);

        if (data.temperature == DEVICE_DISCONNECTED_C) {
            Serial.println("Error reading DS18B20 sensor");
            return false;
        }
        return true;
    }
};

// ============================================================================
// NETWORK MANAGER CLASS
// ============================================================================

class NetworkManager {
public:
    static bool connectToWiFi() {
        if (WiFi.status() == WL_CONNECTED) {
            return true;
        }

        WiFi.mode(WIFI_STA);
        wifiManager.setConnectTimeout(25);

        Serial.println("Attempting to connect to WiFi...");

        if (!wifiManager.autoConnect("Laskakit Meteo Mini Config")) {
            Serial.println("Failed to connect to WiFi");
            handleConnectionFailure();
            return false;
        }

        Serial.println("Connected to WiFi");
        Serial.printf("IP address: %s\n", WiFi.localIP().toString().c_str());
        failedConnectAttempts = 0;
        return true;
    }

    static bool sendToTMEP(const SensorManager::SensorData& data, float batteryVoltage) {
        if (config.serverSendType == ServerType::THINGSPEAK ||
            config.serverSendType == ServerType::NONE) {
            return true; // Skip TMEP
        }

        HTTPClient http;
        String url = "http://" + String(config.serverAddressTmep) +
                    "/?temp=" + String(data.temperature, 2) +
                    "&rssi=" + String(WiFi.RSSI());

        // Add additional parameters based on sensor type
        if (static_cast<uint8_t>(config.sensorType) <= 4) {
            url += "&humV=" + String(data.humidity, 2);
        }

        if (static_cast<uint8_t>(config.sensorType) == 2 ||
            static_cast<uint8_t>(config.sensorType) == 3) {
            url += "&pressV=" + String(data.pressure, 2);
        }

        if (config.sensorType == SensorType::SCD41) {
            url += "&CO2=" + String(data.co2);
        }

        http.begin(url);
        int httpCode = http.GET();
        http.end();

        Serial.printf("TMEP HTTP response: %d\n", httpCode);
        return (httpCode > 0);
    }

    static bool sendToThingSpeak(const SensorManager::SensorData& data, float batteryVoltage) {
        if (config.serverSendType == ServerType::TMEP ||
            config.serverSendType == ServerType::NONE) {
            return true; // Skip ThingSpeak
        }

        if (strlen(config.thingSpeakApiKey) == 0 || strlen(config.thingSpeakChannelId) == 0) {
            Serial.println("Missing ThingSpeak API key or Channel ID");
            return false;
        }

        HTTPClient http;
        String url = "http://api.thingspeak.com/update";
        String postData = "api_key=" + String(config.thingSpeakApiKey);

        // Add temperature
        postData += "&field" + String(config.tsFieldTemp) + "=" + String(data.temperature, 2);

        // Add humidity for supported sensors
        if (static_cast<uint8_t>(config.sensorType) <= 4) {
            postData += "&field" + String(config.tsFieldHum) + "=" + String(data.humidity, 2);
        }

        // Add pressure for BME280
        if (static_cast<uint8_t>(config.sensorType) == 2 ||
            static_cast<uint8_t>(config.sensorType) == 3) {
            postData += "&field" + String(config.tsFieldPress) + "=" + String(data.pressure, 2);
        }

        // Add CO2 for SCD41
        if (config.sensorType == SensorType::SCD41) {
            postData += "&field" + String(config.tsFieldCO2) + "=" + String(data.co2);
        }

        // Add battery voltage
        postData += "&field" + String(config.tsFieldBatt) + "=" + String(batteryVoltage, 2);

        http.begin(url);
        http.addHeader("Content-Type", "application/x-www-form-urlencoded");

        int httpCode = http.POST(postData);
        http.end();

        Serial.printf("ThingSpeak HTTP response: %d\n", httpCode);
        return (httpCode > 0);
    }

private:
    static void handleConnectionFailure() {
        failedConnectAttempts++;

        if (failedConnectAttempts >= MAX_WIFI_CONNECT_ATTEMPTS) {
            Serial.printf("Max connection attempts reached (%d). Entering emergency sleep.\n",
                         MAX_WIFI_CONNECT_ATTEMPTS);
            PowerManager::enterSleep(EMERGENCY_SLEEP_TIME);
        }

        // Progressive backoff
        uint16_t sleepMinutes = min(static_cast<uint16_t>(config.sleepTime),
                                   static_cast<uint16_t>(pow(2, failedConnectAttempts - 1)));
        sleepMinutes = max(sleepMinutes, static_cast<uint16_t>(1));

        Serial.printf("Connection failed (attempt %d/%d). Sleeping for %d minutes.\n",
                     failedConnectAttempts, MAX_WIFI_CONNECT_ATTEMPTS, sleepMinutes);

        PowerManager::enterSleep(sleepMinutes);
    }
};

// ============================================================================
// WIFI CONFIGURATION MANAGER
// ============================================================================

class WiFiConfigManager {
private:
    static WiFiManagerParameter* customParams[13];
    static bool parametersInitialized;

public:
    static void initializeParameters() {
        if (parametersInitialized) return;

        customParams[0] = new WiFiManagerParameter("tmepServer", "TMEP.CZ address", config.serverAddressTmep, 40);
        customParams[1] = new WiFiManagerParameter("thingSpeakKey", "ThingSpeak API Key", config.thingSpeakApiKey, 40);
        customParams[2] = new WiFiManagerParameter("thingSpeakChannel", "ThingSpeak Channel ID", config.thingSpeakChannelId, 20);
        customParams[3] = new WiFiManagerParameter("sleepTime", "Sleep time (minutes)", String(config.sleepTime).c_str(), 6);
        customParams[4] = new WiFiManagerParameter("sensorType", "Sensor type (99=auto)", String(static_cast<uint8_t>(config.sensorType)).c_str(), 2);
        customParams[5] = new WiFiManagerParameter("serverType", "Server type (0=None,1=TMEP,2=ThingSpeak,3=Both)", String(static_cast<uint8_t>(config.serverSendType)).c_str(), 2);
        customParams[6] = new WiFiManagerParameter("sdaPin", "I2C SDA pin", String(config.sdaPin).c_str(), 3);
        customParams[7] = new WiFiManagerParameter("sclPin", "I2C SCL pin", String(config.sclPin).c_str(), 3);
        customParams[8] = new WiFiManagerParameter("tsFieldTemp", "ThingSpeak field - Temperature", String(config.tsFieldTemp).c_str(), 2);
        customParams[9] = new WiFiManagerParameter("tsFieldHum", "ThingSpeak field - Humidity", String(config.tsFieldHum).c_str(), 2);
        customParams[10] = new WiFiManagerParameter("tsFieldPress", "ThingSpeak field - Pressure", String(config.tsFieldPress).c_str(), 2);
        customParams[11] = new WiFiManagerParameter("tsFieldCO2", "ThingSpeak field - CO2", String(config.tsFieldCO2).c_str(), 2);
        customParams[12] = new WiFiManagerParameter("tsFieldBatt", "ThingSpeak field - Battery", String(config.tsFieldBatt).c_str(), 2);

        parametersInitialized = true;
    }

    static void setupWiFiManager() {
        initializeParameters();

        // Add all parameters
        for (int i = 0; i < 13; i++) {
            wifiManager.addParameter(customParams[i]);
        }

        // Configure WiFiManager
        wifiManager.setSaveConfigCallback([]() {
            Serial.println("Configuration saved via WiFiManager");
            saveConfigFromParameters();
        });

        wifiManager.setAPCallback([](WiFiManager* wm) {
            Serial.println("Entered config mode");
            LEDManager::startBlinking();
            portalRunning = true;
        });

        wifiManager.setWebServerCallback([]() {
            LEDManager::update();
        });

        wifiManager.setParamsPage(true);
        wifiManager.setBreakAfterConfig(true);
        wifiManager.setDarkMode(true);
        wifiManager.setTitle("Laskakit Meteo Mini - Configuration");
        wifiManager.setHostname("LaskakitMeteoMini");
        wifiManager.setShowPassword(true);
        wifiManager.setHttpPort(80);
        wifiManager.setWiFiAPChannel(1);

        // Set timeout based on power source
        uint16_t timeout = PowerManager::isRunningOnBattery() ?
                          CONFIG_TIMEOUT_BATTERY : CONFIG_TIMEOUT_EXTERNAL;
        wifiManager.setConfigPortalTimeout(timeout);

        // Custom menu
        std::vector<const char*> menu = {"wifi", "param", "info", "sep", "restart", "sep", "erase", "exit"};
        wifiManager.setMenu(menu);
    }

    static bool startConfigPortal() {
        setupWiFiManager();
        LEDManager::startBlinking();
        portalRunning = true;

        bool result = wifiManager.startConfigPortal("Laskakit Meteo Mini Config");

        portalRunning = false;
        LEDManager::stopBlinking();

        if (result) {
            Serial.println("WiFi configuration completed successfully");
        } else {
            Serial.println("WiFi configuration failed or timed out");
        }

        return result;
    }

private:
    static void saveConfigFromParameters() {
        // Update config from parameters
        strcpy(config.serverAddressTmep, customParams[0]->getValue());
        strcpy(config.thingSpeakApiKey, customParams[1]->getValue());
        strcpy(config.thingSpeakChannelId, customParams[2]->getValue());
        config.sleepTime = atoi(customParams[3]->getValue());
        config.sensorType = static_cast<SensorType>(atoi(customParams[4]->getValue()));
        config.serverSendType = static_cast<ServerType>(atoi(customParams[5]->getValue()));
        config.sdaPin = atoi(customParams[6]->getValue());
        config.sclPin = atoi(customParams[7]->getValue());
        config.tsFieldTemp = atoi(customParams[8]->getValue());
        config.tsFieldHum = atoi(customParams[9]->getValue());
        config.tsFieldPress = atoi(customParams[10]->getValue());
        config.tsFieldCO2 = atoi(customParams[11]->getValue());
        config.tsFieldBatt = atoi(customParams[12]->getValue());

        // Validate and save
        if (!config.isValid()) {
            Serial.println("Invalid configuration received, using defaults");
            config.setDefaults();
        }

        ConfigManager::save();
    }
};

WiFiManagerParameter* WiFiConfigManager::customParams[13];
bool WiFiConfigManager::parametersInitialized = false;

// ============================================================================
// BUTTON HANDLER
// ============================================================================

class ButtonHandler {
public:
    static void checkButton() {
        // Handle portal if running
        if (portalRunning) {
            wifiManager.process();
            LEDManager::update();

            // Auto-close portal after timeout
            if (millis() - portalStartTime > PORTAL_AUTO_CLOSE_TIME) {
                Serial.println("Portal auto-close timeout reached");
                wifiManager.stopWebPortal();
                portalRunning = false;
                LEDManager::stopBlinking();
            }
        }

        // Check button press
        if (digitalRead(TRIGGER_PIN) == LOW) {
            delay(50); // Debounce
            if (digitalRead(TRIGGER_PIN) == LOW) {
                if (!portalRunning) {
                    Serial.println("Button pressed - starting config portal");
                    if (!WiFiConfigManager::startConfigPortal() && PowerManager::isRunningOnBattery()) {
                        PowerManager::enterSleep(config.sleepTime);
                    }
                } else {
                    Serial.println("Button pressed - stopping config portal");
                    wifiManager.stopWebPortal();
                    portalRunning = false;
                    LEDManager::stopBlinking();
                }
            }
        }
    }
};

// ============================================================================
// MAIN FUNCTIONS
// ============================================================================

void setup() {
    Serial.begin(115200);
    delay(100);

    // Initialize hardware
    LEDManager::init();
    pinMode(TRIGGER_PIN, INPUT_PULLUP);

    // Check for emergency low battery
    if (PowerManager::isBatteryLow()) {
        PowerManager::enterEmergencyMode();
    }

    // Boot information
    bootCount++;
    esp_sleep_wakeup_cause_t wakeupReason = esp_sleep_get_wakeup_cause();

    Serial.println("\n=== Laskakit Meteo Mini v2.0 ===");
    Serial.printf("Boot count: %d\n", bootCount);
    Serial.printf("Battery voltage: %.2fV\n", PowerManager::getBatteryVoltage());
    Serial.printf("Power source: %s\n", PowerManager::isRunningOnBattery() ? "Battery" : "External");

    switch (wakeupReason) {
        case ESP_SLEEP_WAKEUP_TIMER:
            Serial.println("Wakeup: Timer");
            break;
        case ESP_SLEEP_WAKEUP_GPIO:
            Serial.println("Wakeup: Button (GPIO)");
            break;
        default:
            Serial.println("Wakeup: Power on or reset");
            break;
    }

    // Initialize EEPROM and load configuration
    EEPROM.begin(EEPROM_SIZE);
    ConfigManager::load();
    ConfigManager::printConfig();

    // Check for configuration mode (button pressed at startup)
    delay(100);
    if (wakeupReason == ESP_SLEEP_WAKEUP_GPIO || digitalRead(TRIGGER_PIN) == LOW) {
        Serial.println("Entering configuration mode...");
        delay(500);
        WiFi.mode(WIFI_STA);

        if (!WiFiConfigManager::startConfigPortal()) {
            if (PowerManager::isRunningOnBattery()) {
                Serial.println("Config failed on battery - entering sleep");
                PowerManager::enterSleep(config.sleepTime);
            }
        } else {
            Serial.println("Configuration completed - continuing with normal operation");
        }
    }

    // Connect to WiFi
    if (!NetworkManager::connectToWiFi()) {
        // Connection failed, sleep handled in NetworkManager
        return;
    }

    // Initialize sensors
    if (!SensorManager::initializeSensors()) {
        Serial.println("WARNING: Sensor initialization failed");
    }

    Serial.println("Setup completed successfully");
}

void loop() {
    // Handle button and portal
    ButtonHandler::checkButton();

    // Skip measurement if portal is running
    if (portalRunning) {
        return;
    }

    // Check WiFi connection
    if (WiFi.status() != WL_CONNECTED) {
        Serial.println("WiFi disconnected - will reconnect on next boot");
        PowerManager::enterSleep(config.sleepTime);
        return;
    }

    // Read sensor data
    Serial.println("Reading sensor data...");
    SensorManager::SensorData sensorData = SensorManager::readSensorData();
    float batteryVoltage = PowerManager::getBatteryVoltage();

    // Print sensor data
    Serial.printf("Temperature: %.2f°C\n", sensorData.temperature);
    if (static_cast<uint8_t>(config.sensorType) <= 4) {
        Serial.printf("Humidity: %.2f%%\n", sensorData.humidity);
    }
    if (static_cast<uint8_t>(config.sensorType) == 2 || static_cast<uint8_t>(config.sensorType) == 3) {
        Serial.printf("Pressure: %.2f hPa\n", sensorData.pressure);
    }
    if (config.sensorType == SensorType::SCD41) {
        Serial.printf("CO2: %d ppm\n", sensorData.co2);
    }
    Serial.printf("Battery: %.2fV\n", batteryVoltage);

    // Validate battery voltage
    if (isnan(batteryVoltage) || batteryVoltage < 0.0f || batteryVoltage > 5.0f) {
        Serial.println("Invalid battery voltage - skipping data transmission");
        sensorData.isValid = false;
    }

    // Send data if valid
    bool sendSuccess = false;
    if (sensorData.isValid) {
        bool tmepSuccess = NetworkManager::sendToTMEP(sensorData, batteryVoltage);
        bool thingSpeakSuccess = NetworkManager::sendToThingSpeak(sensorData, batteryVoltage);

        sendSuccess = tmepSuccess || thingSpeakSuccess;

        if (sendSuccess) {
            Serial.println("Data sent successfully");
        } else {
            Serial.println("Failed to send data to any server");
        }
    } else {
        Serial.println("Invalid sensor data - skipping transmission");
    }

    // Determine sleep duration
    uint16_t sleepDuration = config.sleepTime;
    if (!sendSuccess && PowerManager::isRunningOnBattery()) {
        // Shorter sleep on battery when send fails
        sleepDuration = min(sleepDuration, static_cast<uint16_t>(15));
        Serial.printf("Send failed on battery - shorter sleep: %d minutes\n", sleepDuration);
    }

    // Enter sleep mode
    Serial.printf("Entering sleep for %d minutes\n", sleepDuration);
    PowerManager::enterSleep(sleepDuration);
}
