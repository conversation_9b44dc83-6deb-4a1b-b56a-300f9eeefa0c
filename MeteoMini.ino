
/*
*  Firmware pro Meteo Mini ESP32-C3 s podporou TMEP.CZ a ThingSpeak
*  Umožňuje flexibilní konfiguraci odesíl<PERSON>í dat
*  Obsahuje OnDemand konfiguraci pomocí tlačítka
*  Firmware vyhledá adresy následujíc<PERSON>ch senzorů:
*
*  - SHT40 (0x44)
*  - SHT40 (0x45)
*  - BME280 (0x76)
*  - BME280 (0x77)
*  - SCD41
*  - DS18B20
*
*  Případně lze typ senzoru vynutit, čí<PERSON>ž se automatické vyhledávání zakáže.
*
*  Podle nalezených senzorů se vyplní GET URL:
*  - SHT40: teplota + vlhkost + napětí baterie
*  - BME280: teplota + vlhkost + tlak + napětí baterie
*  - SCD41: teplota + vlhkost + CO2 + napětí baterie
*  - DS18B20: teplota + napětí baterie
*
*  POZOR: Připojte současně POUZE JEDEN senzor!!!
*/

#include <Arduino.h>
#include <SensirionI2cSht4x.h>
#include <Adafruit_BME280.h>
#include <SensirionI2cScd4x.h>
#include <Wire.h>
#include <WiFi.h>
#include <HTTPClient.h>
#include <WiFiManager.h> // https://github.com/tzapu/WiFiManager
#include <DNSServer.h>
#include <WebServer.h>
#include <EEPROM.h>
#include <OneWire.h>
#include <DallasTemperature.h>
#include <esp_sleep.h>
#include <driver/gpio.h>

// Ukládání hodnoty do RTC paměti (přetrvává během deep sleep)
RTC_DATA_ATTR int bootCount = 0;
RTC_DATA_ATTR int failed_connect_attempts = 0; // NOVÁ PROMĚNNÁ pro sledování neúspěšných pokusů o připojení

#define version 2.2  // Verze firmware

// Konfigurace pinů
#define ONE_WIRE_BUS 10 // Pin pro připojení DS18B20 senzoru
#define DIVIDER_RATIO 1.7693877551 // Poměr děliče napětí pro měření baterie
#define PWR_PIN 3 // Pin pro zapínání/vypínání napájení uSup
#define ADC_IN 0 // Analogový vstup pro měření napětí baterie
#define TRIGGER_PIN 2  // Pin pro zapnutí konfiguračního portálu
// Bitová maska pro probuzení z deep sleep je definována přímo v kódu jako 1ULL << TRIGGER_PIN
#define BATTERY_THRESHOLD 3.8 // Prahová hodnota napětí pro detekci napájení z baterie (V) - zvýšeno pro lepší detekci
#define BATTERY_LOW_THRESHOLD 3.3 // Prahová hodnota pro nouzový režim při velmi nízkém napětí
#define CONFIG_TIMEOUT_BATTERY 30 // Timeout konfiguračního portálu při napájení z baterie (sekundy)
#define CONFIG_TIMEOUT_EXTERNAL 120 // Timeout konfiguračního portálu při externím napájení (sekundy) - zvýšeno
#define MAX_WIFI_CONNECT_ATTEMPTS 5 // Maximální počet pokusů o připojení k WiFi před dlouhodobým spánkem
#define EMERGENCY_SLEEP_TIME 60 // Doba spánku v nouzovém režimu (minuty)

bool isRunningOnBattery() {
  float voltage = getBatteryVoltage();
  return (voltage < BATTERY_THRESHOLD);
}

// Výchozí hodnoty pro I2C piny
// Konfigurace I2C pinů - nyní nastavitelné v EEPROM
#define DEFAULT_SDA_PIN 19  //
#define DEFAULT_SCL_PIN 18  //

// Definice verze konfigurace
#define CONFIG_VERSION 1  // Zvýšit při změně struktury konfigurace

// Definice adres v EEPROM
#define EEPROM_SIZE 512

// Výčet druhů serverů pro odesílání dat
enum ServerType {
  NONE = 0,
  TMEP = 1,
  THINGSPEAK = 2,
  BOTH = 3
};

// Struktura pro konfiguraci
struct Config {
  char serverAddressTmep[40];
  char thingSpeakApiKey[40];
  char thingSpeakChannelId[20];
  int sleepTime;
  int sensorType;
  int serverSendType;
  int sdaPin;
  int sclPin;
  int tsFieldTemp;
  int tsFieldHum;
  int tsFieldPress;
  int tsFieldCO2;
  int tsFieldBatt;
  uint8_t configVersion;
};

// Globální instance konfigurace
Config config;

// Globální proměnné pro zpětnou kompatibilitu
char serverAddressTmep[40] = "xny3ef-8khshy.tmep.cz/index.php";
char thingSpeakApiKey[40] = "ABS8T4OULVYA1FOL";     // apiKey pro zápis
char thingSpeakChannelId[20] = "845449";  // Číslo kanálu
int sleepTime = 5;
int sensorType = 99;
int serverSendType = ServerType::BOTH;  // Výchozí nastavení - odesílat na obě platformy
int sdaPin = DEFAULT_SDA_PIN;  // Výchozí SDA pin pro I2C
int sclPin = DEFAULT_SCL_PIN;  // Výchozí SCL pin pro I2C

// Konfigurace ThingSpeak polí
int tsFieldTemp = 2;      // Výchozí field pro teplotu
int tsFieldHum = 3;       // Výchozí field pro vlhkost
int tsFieldPress = 5;     // Výchozí field pro tlak
int tsFieldCO2 = 6;       // Výchozí field pro CO2
int tsFieldBatt = 4;      // Výchozí field pro napětí baterie

// Funkce pro měření napětí baterie s průměrováním
float getBatteryVoltage() {
  float sum = 0;
  const int samples = 5;
  for (int i = 0; i < samples; i++) {
    sum += analogReadMilliVolts(ADC_IN) * DIVIDER_RATIO / 1000;
    delay(10);
  }
  float voltage = sum / samples;
  Serial.print("Napětí baterie: ");
  Serial.print(voltage);
  Serial.println("V");
  return voltage;
}

// Funkce pro detekci napájení z baterie
bool isRunningOnBattery() {
  float voltage = getBatteryVoltage();
  // Pokud je napětí nižší než prahová hodnota, pravděpodobně běžíme na baterii
  return (voltage < BATTERY_THRESHOLD);
}

// Funkce pro kontrolu velmi nízkého napětí baterie (nouzový režim)
bool isBatteryLow() {
  float voltage = getBatteryVoltage();
  return (voltage < BATTERY_LOW_THRESHOLD);
}

// Funkce pro inicializaci konfigurace
void initConfig() {
  // Inicializace konfigurace z globálních proměnných
  strcpy(config.serverAddressTmep, serverAddressTmep);
  strcpy(config.thingSpeakApiKey, thingSpeakApiKey);
  strcpy(config.thingSpeakChannelId, thingSpeakChannelId);
  config.sleepTime = sleepTime;
  config.sensorType = sensorType;
  config.serverSendType = serverSendType;
  config.sdaPin = sdaPin;
  config.sclPin = sclPin;
  config.tsFieldTemp = tsFieldTemp;
  config.tsFieldHum = tsFieldHum;
  config.tsFieldPress = tsFieldPress;
  config.tsFieldCO2 = tsFieldCO2;
  config.tsFieldBatt = tsFieldBatt;
  config.configVersion = CONFIG_VERSION;
}

// Vytvoření instancí
WiFiManager wm;
SensirionI2cSht4x sht4x;
Adafruit_BME280 bme;
SensirionI2cScd4x SCD4X;
OneWire oneWire(ONE_WIRE_BUS);
DallasTemperature DS18B20(&oneWire);
DeviceAddress sensorAddress;

// Proměnná pro sledování stavu konfiguračního portálu
bool portalRunning = false;

// Rozšířené parametry WiFi manageru
WiFiManagerParameter custom_serverAddressTmep("tmepServer", "TMEP.CZ adresa (max 40 znaků)", serverAddressTmep, 40);
WiFiManagerParameter custom_thingSpeakApiKey("thingSpeakKey", "ThingSpeak API klíč", thingSpeakApiKey, 40);
WiFiManagerParameter custom_thingSpeakChannelId("thingSpeakChannel", "ThingSpeak Channel ID", thingSpeakChannelId, 20);
WiFiManagerParameter custom_sleepTime("sleepTime", "Doba spánku (max 200 minut)", String(sleepTime).c_str(), 6);
WiFiManagerParameter custom_sensorType("sensorType", "Typ senzoru (99: auto, 0-5)", String(sensorType).c_str(), 2);
WiFiManagerParameter custom_serverSendType("serverType", "Cíl odesílání (0:Žádný, 1:TMEP, 2:ThingSpeak, 3:Oba)", String(serverSendType).c_str(), 2);
WiFiManagerParameter custom_sdaPin("sdaPin", "I2C SDA pin", String(sdaPin).c_str(), 3);
WiFiManagerParameter custom_sclPin("sclPin", "I2C SCL pin", String(sclPin).c_str(), 3);

// Parametry pro ThingSpeak fieldy (kanály)
WiFiManagerParameter custom_tsFieldTemp("tsFieldTemp", "ThingSpeak field pro teplotu (1-8)", String(tsFieldTemp).c_str(), 2);
WiFiManagerParameter custom_tsFieldHum("tsFieldHum", "ThingSpeak field pro vlhkost (1-8)", String(tsFieldHum).c_str(), 2);
WiFiManagerParameter custom_tsFieldPress("tsFieldPress", "ThingSpeak field pro tlak (1-8)", String(tsFieldPress).c_str(), 2);
WiFiManagerParameter custom_tsFieldCO2("tsFieldCO2", "ThingSpeak field pro CO2 (1-8)", String(tsFieldCO2).c_str(), 2);
WiFiManagerParameter custom_tsFieldBatt("tsFieldBatt", "ThingSpeak field pro napětí baterie (1-8)", String(tsFieldBatt).c_str(), 2);

// Funkce pro validaci konfigurace
void validateConfig() {
  if (config.sleepTime < 1 || config.sleepTime > 200) {
    Serial.println("Neplatná doba spánku, nastavuji na výchozích 5 minut");
    config.sleepTime = 5;
  }

  if (config.sensorType < 0 || (config.sensorType > 5 && config.sensorType != 99)) {
    Serial.println("Neplatný typ senzoru, nastavuji na auto-detekci");
    config.sensorType = 99;
  }

  if (config.serverSendType < 0 || config.serverSendType > 3) {
    Serial.println("Neplatný typ odesílání, nastavuji na obě platformy");
    config.serverSendType = ServerType::BOTH;
  }

  if (config.sdaPin < 0 || config.sdaPin > 40) {
    Serial.println("Neplatný SDA pin, nastavuji na výchozí hodnotu");
    config.sdaPin = DEFAULT_SDA_PIN;
  }

  if (config.sclPin < 0 || config.sclPin > 40) {
    Serial.println("Neplatný SCL pin, nastavuji na výchozí hodnotu");
    config.sclPin = DEFAULT_SCL_PIN;
  }

  // Validace ThingSpeak fieldů (1-8)
  if (config.tsFieldTemp < 1 || config.tsFieldTemp > 8) config.tsFieldTemp = 2;
  if (config.tsFieldHum < 1 || config.tsFieldHum > 8) config.tsFieldHum = 3;
  if (config.tsFieldPress < 1 || config.tsFieldPress > 8) config.tsFieldPress = 5;
  if (config.tsFieldCO2 < 1 || config.tsFieldCO2 > 8) config.tsFieldCO2 = 6;
  if (config.tsFieldBatt < 1 || config.tsFieldBatt > 8) config.tsFieldBatt = 4;
}

// Funkce pro uložení konfigurace do EEPROM
void saveConfig() {
  // Nastavení verze konfigurace
  config.configVersion = CONFIG_VERSION;

  // Uložení celé struktury do EEPROM
  EEPROM.put(0, config);
  EEPROM.commit();

  Serial.println("Konfigurace uložena do EEPROM");
}

// Funkce pro načtení konfigurace z EEPROM
bool loadConfig() {
  // Načtení celé struktury z EEPROM
  EEPROM.get(0, config);

  // Kontrola verze konfigurace
  if (config.configVersion != CONFIG_VERSION) {
    Serial.println("Neplatná verze konfigurace, nastavuji výchozí hodnoty");
    resetConfig();
    return false;
  }

  // Validace načtených hodnot
  validateConfig();
  return true;
}

// Funkce pro aktualizaci globálních proměnných z konfigurace
void updateGlobalVariables() {
  // Aktualizace globálních proměnných z konfigurace
  strcpy(serverAddressTmep, config.serverAddressTmep);
  strcpy(thingSpeakApiKey, config.thingSpeakApiKey);
  strcpy(thingSpeakChannelId, config.thingSpeakChannelId);
  sleepTime = config.sleepTime;
  sensorType = config.sensorType;
  serverSendType = config.serverSendType;
  sdaPin = config.sdaPin;
  sclPin = config.sclPin;
  tsFieldTemp = config.tsFieldTemp;
  tsFieldHum = config.tsFieldHum;
  tsFieldPress = config.tsFieldPress;
  tsFieldCO2 = config.tsFieldCO2;
  tsFieldBatt = config.tsFieldBatt;
}

// Funkce pro reset konfigurace na výchozí hodnoty
void resetConfig() {
  // Resetování globálních proměnných na výchozí hodnoty
  strcpy(serverAddressTmep, "xny3ef-8khshy.tmep.cz/index.php");
  strcpy(thingSpeakApiKey, "ABS8T4OULVYA1FOL");
  strcpy(thingSpeakChannelId, "845449");
  sleepTime = 5;
  sensorType = 99;
  serverSendType = ServerType::BOTH;
  sdaPin = DEFAULT_SDA_PIN;
  sclPin = DEFAULT_SCL_PIN;
  tsFieldTemp = 2;
  tsFieldHum = 3;
  tsFieldPress = 5;
  tsFieldCO2 = 6;
  tsFieldBatt = 4;

  // Inicializace konfigurace z globálních proměnných
  initConfig();

  // Uložení konfigurace do EEPROM
  saveConfig();

  Serial.println("Konfigurace resetována na výchozí hodnoty");
}

void saveConfigCallback() {
  Serial.println("Ukládání konfigurace...");

  // Uložení parametrů do struktury konfigurace
  strcpy(config.serverAddressTmep, custom_serverAddressTmep.getValue());
  strcpy(config.thingSpeakApiKey, custom_thingSpeakApiKey.getValue());
  strcpy(config.thingSpeakChannelId, custom_thingSpeakChannelId.getValue());
  config.sleepTime = atoi(custom_sleepTime.getValue());
  config.sensorType = atoi(custom_sensorType.getValue());
  config.serverSendType = atoi(custom_serverSendType.getValue());
  config.sdaPin = atoi(custom_sdaPin.getValue());
  config.sclPin = atoi(custom_sclPin.getValue());

  // Uložení ThingSpeak fieldů
  config.tsFieldTemp = atoi(custom_tsFieldTemp.getValue());
  config.tsFieldHum = atoi(custom_tsFieldHum.getValue());
  config.tsFieldPress = atoi(custom_tsFieldPress.getValue());
  config.tsFieldCO2 = atoi(custom_tsFieldCO2.getValue());
  config.tsFieldBatt = atoi(custom_tsFieldBatt.getValue());

  // Validace a uložení konfigurace
  validateConfig();
  saveConfig();

  // Aktualizace globálních proměnných
  updateGlobalVariables();
}

// Funkce pro úplný reset nastavení
void fullReset() {
  // Resetování WiFi nastavení
  wm.resetSettings();

  // Resetování konfigurace na výchozí hodnoty
  resetConfig();

  Serial.println("Všechna nastavení byla resetována na výchozí hodnoty");
}

// Funkce pro nastavení menu WiFiManageru
void setupWiFiManager() {

  // Resetování WiFi nastavení
  wm.resetSettings();

  // Přidání parametrů do WiFi manageru
  wm.addParameter(&custom_serverAddressTmep);
  wm.addParameter(&custom_thingSpeakApiKey);
  wm.addParameter(&custom_thingSpeakChannelId);
  wm.addParameter(&custom_sleepTime);
  wm.addParameter(&custom_sensorType);
  wm.addParameter(&custom_serverSendType);
  wm.addParameter(&custom_sdaPin);
  wm.addParameter(&custom_sclPin);

  // Přidání parametrů pro ThingSpeak fieldy
  wm.addParameter(&custom_tsFieldTemp);
  wm.addParameter(&custom_tsFieldHum);
  wm.addParameter(&custom_tsFieldPress);
  wm.addParameter(&custom_tsFieldCO2);
  wm.addParameter(&custom_tsFieldBatt);

  // Nastavení callback funkce pro uložení konfigurace
  wm.setSaveConfigCallback(saveConfigCallback);

  // Nastavení parametrů na vlastní stránku a oddělení od WiFi stránky
  wm.setParamsPage(true);

  // Nastavení, aby se callback volal i při neúspěšném připojení k WiFi
  wm.setBreakAfterConfig(true);

// Nastavení vlastního menu s více položkami
// Odstraněna položka "update", protože není implementována funkce pro aktualizaci firmware
std::vector<const char *> menu = {"wifi","param","info","sep","restart","sep","erase","exit"};
wm.setMenu(menu);

  // Nastavení tmavého režimu
  wm.setDarkMode(true);

  // Nastavení vlastního titulku
  wm.setTitle("Laskakit Meteo Mini - Konfigurace");

  // Nastavení hostname
  wm.setHostname("LaskakitMeteoMini");

  // Nastavení timeoutu pfirmwarero konfigurační portál podle typu napájení
  if (isRunningOnBattery()) {
    Serial.println("Napájení z baterie - nastavuji kratší timeout");
    wm.setConfigPortalTimeout(CONFIG_TIMEOUT_BATTERY); // Kratší timeout při napájení z baterie
  } else {
    Serial.println("Externí napájení - nastavuji delší timeout");
    wm.setConfigPortalTimeout(CONFIG_TIMEOUT_EXTERNAL); // Delší timeout při externím napájení
  }

  // Nastavení zobrazení tlačítek na informační stránce
  wm.setShowInfoErase(true);
  // Odstraněno wm.setShowInfoUpdate(true), protože není implementována funkce pro aktualizaci firmware

  // Nastavení vlastního HTML kódu na konec stránky
  const char* footerHtml = "<p>Firmware verze 2.2 </p>";
  wm.setCustomBodyFooter(footerHtml);

  // Nastavení zobrazení hesla
  wm.setShowPassword(true);

  // Nastavení HTTP portu
  wm.setHttpPort(80);

  // Nastavení WiFi kanálu pro AP
  wm.setWiFiAPChannel(1);
}

// Funkce pro kontrolu OnDemand konfigurace
void checkButton() {
  // Pokud je portál spuštěn, zpracovat požadavky
  if (portalRunning) {
    wm.process();
  }

  // Kontrola, zda je tlačítko stisknuto
  if (digitalRead(TRIGGER_PIN) == LOW) {
    delay(50);  // Debounce
    if (digitalRead(TRIGGER_PIN) == LOW) {
      if (!portalRunning) {
        Serial.println("Tlačítko stisknuto, spouštím konfigurační portál");

        // Inicializace EEPROM před resetováním nastavení (pro jistotu, i když by měla být již inicializována)
        EEPROM.begin(EEPROM_SIZE);

        // Resetování všech nastavení před vstupem do konfiguračního režimu
        fullReset();

        // Nastavení WiFiManageru
        setupWiFiManager();

        // Spuštění konfiguračního portálu
        if (!wm.startConfigPortal("Laskakit Meteo Mini Config")) {
          Serial.println("Nepodařilo se spustit konfigurační portál nebo vypršel timeout");
          portalRunning = false;

           // Pokud jsme na baterii, přejdeme do režimu spánku
          if (isRunningOnBattery()) {
            Serial.println("Napájení z baterie - přecházím do režimu spánku");
            esp_sleep_enable_timer_wakeup(sleepTime * 60 * 1000000ULL);
            esp_deep_sleep_start();
          }
        } else {
          Serial.println("Připojeno k WiFi přes konfigurační portál");
          // Nastavíme portalRunning na true, aby wm.process() byl volán v checkButton()
          portalRunning = true;

                  }
      } else {
        Serial.println("Tlačítko stisknuto, zastavuji konfigurační portál");
        wm.stopWebPortal();
        portalRunning = false;

       }
    }
  }
}

// Funkce pro odeslání dat na ThingSpeak
bool sendToThingSpeak(float temperature, float humidity, float pressure, uint16_t co2, float batteryVoltage) {
  // Kontrola, zda jsou vyplněny povinné údaje
  if (strlen(thingSpeakApiKey) == 0 || strlen(thingSpeakChannelId) == 0) {
    Serial.println("Chybí ThingSpeak API klíč nebo Channel ID");
    return false;
  }

  HTTPClient http;
  String url = "http://api.thingspeak.com/update";

  // Dynamické vytvoření POST requestu podle konfigurace
  String postData = String("api_key=") + thingSpeakApiKey;

  // Přidání hodnot podle nakonfigurovaných fieldů
  postData += "&field" + String(tsFieldTemp) + "=" + String(temperature);

  if ((sensorType == 0) || (sensorType == 1) || (sensorType == 2) || (sensorType == 3) || (sensorType == 4)) {
    postData += "&field" + String(tsFieldHum) + "=" + String(humidity);
  }

  if ((sensorType == 2) || (sensorType == 3)) {
    postData += "&field" + String(tsFieldPress) + "=" + String(pressure);
  }

  if (sensorType == 4) {
    postData += "&field" + String(tsFieldCO2) + "=" + String(co2);
  }

  postData += "&field" + String(tsFieldBatt) + "=" + String(batteryVoltage);

  http.begin(url);
  http.addHeader("Content-Type", "application/x-www-form-urlencoded");

  int httpResponseCode = http.POST(postData);
  http.end();

  Serial.print("ThingSpeak HTTP kód: ");
  Serial.println(httpResponseCode);

  return (httpResponseCode > 0);
}

void setup() {
  // Inicializace sériové komunikace
  Serial.begin(115200);
  delay(100);

  // Kontrola nouzového režimu při velmi nízkém napětí baterie
  if (isBatteryLow()) {
    Serial.printf("NOUZOVÝ REŽIM: Velmi nízké napětí baterie (%.2fV < %.2fV)!\n",
                  getBatteryVoltage(), BATTERY_LOW_THRESHOLD);
    Serial.printf("Přecházím do dlouhodobého spánku na %d minut.\n", EMERGENCY_SLEEP_TIME);
    esp_sleep_enable_timer_wakeup(EMERGENCY_SLEEP_TIME * 60 * 1000000ULL);
    esp_deep_sleep_start();
  }

  // Inkrementace počtu bootů
  bootCount++;

  // Výpis důvodu probuzení
  esp_sleep_wakeup_cause_t wakeup_reason = esp_sleep_get_wakeup_cause();

  Serial.println("\n Starting");
  Serial.printf("Boot count: %d\n", bootCount);

  // Výpis důvodu probuzení
  switch(wakeup_reason) {
    case ESP_SLEEP_WAKEUP_TIMER:
      Serial.println("Probuzení způsobeno časovačem");
      break;
    case ESP_SLEEP_WAKEUP_GPIO:
      Serial.println("Probuzení způsobeno tlačítkem (GPIO)");
      break;
    default:
      Serial.printf("Probuzení nebylo způsobeno deep sleep: %d\n", wakeup_reason);
      break;
  }

  // Kontrola stavu vstupního pinu tlačítka při startu
  pinMode(TRIGGER_PIN, INPUT_PULLUP);


  // Přidáno zpoždění pro stabilizaci po probuzení z deep sleep
  delay(100);

  // Kontrola, zda bylo probuzení způsobeno tlačítkem nebo je tlačítko stisknuto při startu
  if (wakeup_reason == ESP_SLEEP_WAKEUP_GPIO || digitalRead(TRIGGER_PIN) == LOW) {
    Serial.println("Tlačítko je stisknuto nebo probuzení bylo způsobeno tlačítkem. Vstupuji do konfiguračního režimu.");

    
    // Delší zpoždění pro stabilizaci systému před spuštěním WiFiManager
    delay(500);

    // Inicializace EEPROM před resetováním nastavení
    EEPROM.begin(EEPROM_SIZE);

    // Resetování všech nastavení před vstupem do konfiguračního režimu
    fullReset();

    // Inicializace WiFi a spuštění konfiguračního portálu
    WiFi.mode(WIFI_STA);

    // Nastavení WiFiManageru
    setupWiFiManager();


    // Spuštění konfiguračního portálu
    if (!wm.startConfigPortal("Laskakit Meteo Mini Config")) {
      Serial.println("Nepodařilo se spustit konfigurační portál nebo vypršel timeout");

     
      // Pokud jsme na baterii, přejdeme do režimu spánku
      if (isRunningOnBattery()) {
        Serial.println("Napájení z baterie - přecházím do režimu spánku");
        esp_sleep_enable_timer_wakeup(sleepTime * 60 * 1000000ULL);
        esp_deep_sleep_start();
      }
      // Jinak pokračujeme v normálním běhu programu
    } else {
      Serial.println("Připojeno k WiFi přes konfigurační portál");
      // Nastavíme portalRunning na true, aby wm.process() byl volán v checkButton()
      portalRunning = true;
        }

    // Pokračujeme v normálním běhu programu místo return
    // Tím se vyhneme problémům s nestabilitou
  }

  // Inicializace EEPROM a načtení uložené konfigurace
  EEPROM.begin(EEPROM_SIZE);

  // Inicializace konfigurace z globálních proměnných
  initConfig();

  // Nastavení pinu pro napájení senzorů (ale zatím nezapínat)
  pinMode(PWR_PIN, OUTPUT);
  digitalWrite(PWR_PIN, LOW);  // Napájení senzorů zatím vypnuto pro úsporu energie

  Serial.println("-------------------");
  Serial.println("Laskakit Meteo Mini");
  Serial.println("TMEP a ThingSpeak firmware");
  Serial.println("Podpora sezorů SHT40 (0x44, 0x45), BME280 (0x76, 0x77), SCD41, DS18B20");
  Serial.println("!!POZOR!! připojit pouze jeden senzor !!POZOR!!");
  Serial.print("verze: "); Serial.println(version);
  Serial.println("-------------------");

  // Načtení konfigurace z EEPROM
  if (loadConfig()) {
    Serial.println("Konfigurace úspěšně načtena z EEPROM");
    // Aktualizace globálních proměnných
    updateGlobalVariables();
  } else {
    Serial.println("Načtení konfigurace selhalo, používám výchozí hodnoty");
    // Pokud načtení selhalo, resetConfig() již nastavil výchozí hodnoty
  }

  Serial.println("Uložená adresa TMEP serveru: " + String(serverAddressTmep));
  Serial.println("Uložený ThingSpeak Channel ID: " + String(thingSpeakChannelId));
  Serial.println("Doba spánku: " + String(sleepTime) + " minut");
  Serial.println("Typ senzoru: " + String(sensorType));
  Serial.println("Odesílání dat: " + String(serverSendType));
  Serial.println("I2C SDA pin: " + String(sdaPin));
  Serial.println("I2C SCL pin: " + String(sclPin));

  // Aktualizace hodnot v parametrech - přesunuto před setupWiFiManager()
  // Parametry jsou již přidány v setupWiFiManager(), zde je pouze aktualizujeme

  // Aktualizace hodnot v parametrech
  custom_sleepTime.setValue(String(sleepTime).c_str(), String(sleepTime).length());
  custom_sensorType.setValue(String(sensorType).c_str(), String(sensorType).length());
  custom_serverSendType.setValue(String(serverSendType).c_str(), String(serverSendType).length());
  custom_sdaPin.setValue(String(sdaPin).c_str(), String(sdaPin).length());
  custom_sclPin.setValue(String(sclPin).c_str(), String(sclPin).length());

  // Aktualizace hodnot ThingSpeak fieldů
  custom_tsFieldTemp.setValue(String(tsFieldTemp).c_str(), String(tsFieldTemp).length());
  custom_tsFieldHum.setValue(String(tsFieldHum).c_str(), String(tsFieldHum).length());
  custom_tsFieldPress.setValue(String(tsFieldPress).c_str(), String(tsFieldPress).length());
  custom_tsFieldCO2.setValue(String(tsFieldCO2).c_str(), String(tsFieldCO2).length());
  custom_tsFieldBatt.setValue(String(tsFieldBatt).c_str(), String(tsFieldBatt).length());

  // Nastavení callback funkce pro uložení konfigurace
  wm.setSaveConfigCallback(saveConfigCallback);

  // Přidání parametrů na vlastní stránku a oddělení od WiFi stránky
  wm.setParamsPage(true);

  // Nastavení, aby se callback volal i při neúspěšném připojení k WiFi
  wm.setBreakAfterConfig(true);

  // Připojení k WiFi, pokud jsme ještě nepřipojeni
  if (WiFi.status() != WL_CONNECTED) {
    WiFi.mode(WIFI_STA);

    // Nastavení delšího timeoutu pro autoConnect - zvýšeno pro stabilnější připojení
    wm.setConnectTimeout(25); // Zvýšeno z 10 na 25 sekund pro lepší stabilitu

    Serial.println("Pokus o připojení k WiFi...");
    if (!wm.autoConnect("Laskakit Meteo Mini Config")) {
      Serial.println("Nepodařilo se připojit k WiFi.");

      // Zvýšíme počet neúspěšných pokusů
      failed_connect_attempts++;

      // Kontrola, zda jsme překročili maximální počet pokusů
      if (failed_connect_attempts >= MAX_WIFI_CONNECT_ATTEMPTS) {
        Serial.printf("Překročen maximální počet pokusů (%d). Přecházím do dlouhodobého spánku na %d minut.\n",
                      MAX_WIFI_CONNECT_ATTEMPTS, EMERGENCY_SLEEP_TIME);
        esp_sleep_enable_timer_wakeup(EMERGENCY_SLEEP_TIME * 60 * 1000000ULL);
        esp_deep_sleep_start();
      }

      // Vypočítáme progresivní dobu spánku, např. 1, 2, 4, 8, 15 minut... až po max. sleepTime
      long sleep_duration_minutes = min((long)sleepTime, (long)pow(2, failed_connect_attempts -1));

      // Aby to nebylo hned napoprvé moc dlouhé a zároveň aby 0 pokusů dalo smysl (i když by se sem nemělo dostat)
      if (sleep_duration_minutes < 1) sleep_duration_minutes = 1;

      Serial.printf("Nepřipojeno (pokus %d/%d). Zkouším znovu za %ld minut.\n",
                    failed_connect_attempts, MAX_WIFI_CONNECT_ATTEMPTS, sleep_duration_minutes);

      esp_sleep_enable_timer_wakeup(sleep_duration_minutes * 60 * 1000000ULL);
      esp_deep_sleep_start();
    } else {
      // Pokud se připojení podařilo, resetujeme počítadlo
      failed_connect_attempts = 0;
    }
  }

  Serial.println("Připojeno k WiFi");

  // Zapnutí napájení senzorů těsně před jejich inicializací
  Serial.println("Zapínám napájení senzorů...");
  digitalWrite(PWR_PIN, HIGH);
  delay(100); // Krátké zpoždění pro stabilizaci napájení

  // Inicializace I2C s nastavenými piny
  Wire.begin(sdaPin, sclPin); // Použití nakonfigurovaných pinů

  // Inicializace OneWire
  DS18B20.begin(); // 10 - DATA

  // Inicializace a detekce senzorů
  uint16_t error;
  char errorMessage[256];
  bool sensorFound = false;

  /* SHT40 - 0x44 */
  if((sensorType == 0) || (sensorType == 99))
  {
    // Inicializace senzoru SHT40 na adrese 0x44
    sht4x.begin(Wire, 0x44);

    // Kontrola, zda je senzor dostupný pomocí čtení sériového čísla
    uint32_t serialNumber;
    error = sht4x.serialNumber(serialNumber);
    if (error)
    {
      Serial.println("Nelze najít platný snímač SHT40 (0x44), zkontrolujte zapojení!");
    }
    else
    {
      Serial.println("Senzor SHT40 (0x44) nalezen");
      sensorType = 0;
      sensorFound = true;
    }
  }

  // Pokračování s ostatními senzory pouze pokud ještě žádný nebyl nalezen
  if (!sensorFound && ((sensorType == 1) || (sensorType == 99)))
  {
    // Inicializace senzoru SHT40 na adrese 0x45
    sht4x.begin(Wire, 0x45);

    // Kontrola, zda je senzor dostupný pomocí čtení sériového čísla
    uint32_t serialNumber;
    error = sht4x.serialNumber(serialNumber);
    if (error)
    {
      Serial.println("Nelze najít platný snímač SHT40 (0x45), zkontrolujte zapojení!");
    }
    else
    {
      Serial.println("Senzor SHT40 (0x45) nalezen");
      sensorType = 1;
      sensorFound = true;
    }
  }

  if (!sensorFound && ((sensorType == 2) || (sensorType == 99)))
  {
    if (!bme.begin(0x76))
    {
      Serial.println("Nelze najít platný snímač BME280 (0x76), zkontrolujte zapojení!");
    }
    else
    {
      Serial.println("Senzor BME280 (0x76) nalezen");
      sensorType = 2;
      sensorFound = true;

      // bme setting
      bme.setSampling(Adafruit_BME280::MODE_FORCED, // Rychlé čtení ze senzoru
                Adafruit_BME280::SAMPLING_X1, // Vzorkování teploty nastaveno na 1
                Adafruit_BME280::SAMPLING_X1, // Vzorkování tlaku nastaveno na 1
                Adafruit_BME280::SAMPLING_X1, // Vzorkování vlhkosti nastaveno na 1
                Adafruit_BME280::FILTER_OFF   // Filtr vypnut - okamžitá 100% kroková odezva
                );
    }
  }

  if (!sensorFound && ((sensorType == 3) || (sensorType == 99)))
  {
    if (!bme.begin(0x77))
    {
      Serial.println("Nelze najít platný snímač BME280 (0x77), zkontrolujte zapojení!");
    }
    else
    {
      Serial.println("Senzor BME280 (0x77) nalezen");
      sensorType = 3;
      sensorFound = true;

      // bme setting
      bme.setSampling(Adafruit_BME280::MODE_FORCED, // Rychlé čtení ze senzoru
                Adafruit_BME280::SAMPLING_X1, // Vzorkování teploty nastaveno na 1
                Adafruit_BME280::SAMPLING_X1, // Vzorkování tlaku nastaveno na 1
                Adafruit_BME280::SAMPLING_X1, // Vzorkování vlhkosti nastaveno na 1
                Adafruit_BME280::FILTER_OFF   // Filtr vypnut - okamžitá 100% kroková odezva
                );
    }
  }

  if (!sensorFound && ((sensorType == 4) || (sensorType == 99)))
  {
    SCD4X.begin(Wire, 0x62);

    error = SCD4X.startPeriodicMeasurement();
    if (error)
    {
      Serial.println("Nelze najít platný snímač SCD41, zkontrolujte zapojení!");
    }
    else
    {
      Serial.println("Senzor SCD41 nalezen");
      sensorType = 4;
      sensorFound = true;
      // Přidání delšího zpoždění pro SCD41, který potřebuje čas na zahřátí
      delay(5000);
    }
  }

  if (!sensorFound && ((sensorType == 5) || (sensorType == 99)))
  {
    // Zkontrolujte, zda je na sběrnici OneWire přítomno nějaké zařízení
    if (!oneWire.search(sensorAddress))
    {
      Serial.println("Nelze najít platný snímač DS18B20, zkontrolujte zapojení!");
      oneWire.reset_search();
    }
    else
    {
      Serial.println("Senzor DS18B20 nalezen");
      sensorType = 5;
      sensorFound = true;
    }
  }

  // Pokud nebyl nalezen žádný senzor a není vynucen konkrétní typ
  if (!sensorFound && sensorType == 99) {
    Serial.println("VAROVÁNÍ: Nebyl nalezen žádný senzor!");
  }
}

void loop() {
  // Kontrola tlačítka pro OnDemand konfiguraci
  checkButton();

  // Pokud běží konfigurační portál, neprovádět měření
  if (portalRunning) {
    delay(100);   // Malé zpoždění pro optimalizaci
    return;
  }

  // Pokud WiFi není připojeno, nebudeme se zde pokoušet o znovupřipojení.
  // Logika pro připojení a progresivní backoff je již v setup().
  // Zařízení půjde spát a při dalším probuzení se pokusí připojit v setup().
  if (WiFi.status() != WL_CONNECTED) {
    Serial.println("WiFi není připojeno. Přeskočení měření a odesílání.");
    // sendSuccess zůstane false, což ovlivní logiku uspávání níže
  }


  // Měření dat
  float temperature = 0;
  float humidity = 0;
  float pressure = 0;
  uint16_t co2 = 0;
  float battery_voltage = 0;

  // Měření napětí baterie pomocí funkce s průměrováním
  battery_voltage = getBatteryVoltage();

  delay(100);

  // Blok pro měření dat ze senzorů
  bool sensorReadSuccess = false;
  uint16_t error;
  char errorMessage[256];

  if ((sensorType == 0) || (sensorType == 1)) // SHT40 0x44 nebo 0x45
  {
    error = sht4x.measureHighPrecision(temperature, humidity);
    if (error)
    {
      Serial.print("Error trying to execute measureHighPrecision(): ");
      errorToString(error, errorMessage, 256);
      Serial.println(errorMessage);
      // Nastavíme sensorReadSuccess na false, aby se data neodesílala
      sensorReadSuccess = false;
      // Nastavíme hodnoty na 0, aby nedošlo k odeslání neplatných dat
      temperature = 0;
      humidity = 0;
    }
    else
    {
      // Kontrola, zda jsou hodnoty v rozumném rozsahu
      if (isnan(temperature) || temperature < -40 || temperature > 85 || isnan(humidity) || humidity < 0 || humidity > 100) {
        Serial.println("Neplatné hodnoty ze senzoru SHT40");
        sensorReadSuccess = false;
      } else {
        sensorReadSuccess = true;
      }
    }
  }
  else if ((sensorType == 2) || (sensorType == 3)) // BME280 0x76 nebo 0x77
  {
    bme.takeForcedMeasurement(); // Spustit měření
    temperature = bme.readTemperature();
    humidity = bme.readHumidity();
    pressure = bme.readPressure() / 100.0F;

    // Kontrola, zda jsou hodnoty validní
    if (isnan(temperature) || isnan(humidity) || isnan(pressure) ||
        temperature < -40 || temperature > 85 ||
        humidity < 0 || humidity > 100 ||
        pressure < 300 || pressure > 1100) {
      Serial.println("Chyba při čtení dat z BME280 senzoru nebo neplatné hodnoty");
      sensorReadSuccess = false;
      // Nastavíme hodnoty na 0, aby nedošlo k odeslání neplatných dat
      temperature = 0;
      humidity = 0;
      pressure = 0;
    }
    else
    {
      sensorReadSuccess = true;
    }
  }
  else if (sensorType == 4) // SCD41
  {
    // SCD41 potřebuje více času na měření
    delay(5000);
    error = SCD4X.readMeasurement(co2, temperature, humidity);
    if (error)
    {
      Serial.print("Error trying to execute readMeasurement(): ");
      errorToString(error, errorMessage, 256);
      Serial.println(errorMessage);
      // Nastavíme sensorReadSuccess na false, aby se data neodesílala
      sensorReadSuccess = false;
      // Nastavíme hodnoty na 0, aby nedošlo k odeslání neplatných dat
      co2 = 0;
      temperature = 0;
      humidity = 0;
    }
    else
    {
      // Kontrola, zda jsou hodnoty v rozumném rozsahu
      if (co2 < 400 || co2 > 5000 || isnan(temperature) || isnan(humidity)) {
        Serial.println("Neplatné hodnoty ze senzoru SCD41");
        sensorReadSuccess = false;
      } else {
        sensorReadSuccess = true;
      }
    }
  }
  else if(sensorType == 5) // DS18B20
  {
    // Request temperature readings
    DS18B20.requestTemperatures();
    // Fetch temperature in Celsius
    temperature = DS18B20.getTempC(sensorAddress);

    if (temperature == DEVICE_DISCONNECTED_C || isnan(temperature) || temperature < -40 || temperature > 85) {
      Serial.println("Chyba při čtení teploty z DS18B20 senzoru nebo neplatná hodnota");
      sensorReadSuccess = false;
      // Nastavíme hodnotu na 0, aby nedošlo k odeslání neplatných dat
      temperature = 0;
    }
    else
    {
      sensorReadSuccess = true;
    }
  }

  /* Print to Serial Monitor */
  Serial.print("Temperature: ");
  Serial.print(temperature);
  Serial.println(" °C");

  if ((sensorType == 0) || (sensorType == 1) || (sensorType == 2) || (sensorType == 3) || (sensorType == 4))
  {
    Serial.print("Humidity: ");
    Serial.print(humidity);
    Serial.println(" %");
  }
  if ((sensorType == 2) || (sensorType == 3))
  {
    Serial.print("Pressure: ");
    Serial.print(pressure);
    Serial.println(" hPa");
  }
  if (sensorType == 4)
  {
    Serial.print("CO2: ");
    Serial.print(co2);
    Serial.println(" ppm");
  }

  Serial.print("Battery voltage: ");
  Serial.print(battery_voltage);
  Serial.println(" V");

  // Kontrola platnosti naměřených hodnot
  bool validData = true;

  // Kontrola teploty
  if (isnan(temperature) || temperature < -40 || temperature > 85) {
    Serial.println("Neplatná hodnota teploty: " + String(temperature));
    validData = false;
  }

  // Kontrola vlhkosti
  if ((sensorType == 0 || sensorType == 1 || sensorType == 2 || sensorType == 3 || sensorType == 4) &&
      (isnan(humidity) || humidity < 0 || humidity > 100)) {
    Serial.println("Neplatná hodnota vlhkosti: " + String(humidity));
    validData = false;
  }

  // Kontrola tlaku
  if ((sensorType == 2 || sensorType == 3) &&
      (isnan(pressure) || pressure < 300 || pressure > 1100)) {
    Serial.println("Neplatná hodnota tlaku: " + String(pressure));
    validData = false;
  }

  // Kontrola CO2
  if (sensorType == 4 && (co2 < 400 || co2 > 5000)) {
    Serial.println("Neplatná hodnota CO2: " + String(co2));
    validData = false;
  }

  // Kontrola napětí baterie
  if (isnan(battery_voltage) || battery_voltage < 0 || battery_voltage > 5) {
    Serial.println("Neplatná hodnota napětí baterie: " + String(battery_voltage));
    validData = false;
  }

  // Otestování, zda je WiFi připojeno a odeslání dat
  bool sendSuccess = false;

  if (WiFi.status() == WL_CONNECTED && validData && sensorReadSuccess) {

    // Odeslání na TMEP.CZ
    if ((serverSendType == ServerType::TMEP || serverSendType == ServerType::BOTH) && strlen(serverAddressTmep) > 0) {
      HTTPClient http;
      String url = "http://" + String(serverAddressTmep) + "/?temp=" + String(temperature) + "&rssi=" + WiFi.RSSI();

      // Přidání dalších parametrů podle typu senzoru
      if ((sensorType == 0) || (sensorType == 1) || (sensorType == 2) || (sensorType == 3) || (sensorType == 4))
      {
        url += "&humV=" + String(humidity);
      }
      if ((sensorType == 2) || (sensorType == 3))
      {
        url += "&pressV=" + String(pressure);
      }
      if (sensorType == 4)
      {
        url += "&CO2=" + String(co2);
      }
      url += "&voltage=" + String(battery_voltage);

      http.begin(url);
      Serial.println(url);
      int httpResponseCode = http.GET();
      http.end();

      if (httpResponseCode > 0) {
        Serial.println("TMEP HTTP kód: " + String(httpResponseCode));
        sendSuccess = true;
      } else {
        Serial.println("Chyba v HTTP požadavku na TMEP");
      }
    }

    // Odeslání na ThingSpeak
    if (serverSendType == ServerType::THINGSPEAK || serverSendType == ServerType::BOTH) {
      bool thingSpeakResult = sendToThingSpeak(temperature, humidity, pressure, co2, battery_voltage);
      sendSuccess |= thingSpeakResult;
    }

    if (!sendSuccess) {
      Serial.println("Nepodařilo se odeslat data na žádný server");
    }
  } else {
    if (WiFi.status() != WL_CONNECTED) {
        Serial.println("WiFi není připojeno, data neodesílám.");
    } else { // validData nebo sensorReadSuccess je false
        Serial.println("Neplatná data ze senzoru nebo chyba čtení, neodesílám.");
    }
    // sendSuccess zůstane false
  }

  Serial.print("Meteo Mini přechází do spánku na ");
  Serial.print(sleepTime);
  Serial.println(" minut");


  digitalWrite(PWR_PIN, LOW); // Vypnutí napájení senzorů
  Serial.flush();
  delay(100);

  // Kontrola nouzového režimu před uspáním
  if (isBatteryLow()) {
    Serial.printf("NOUZOVÝ REŽIM: Velmi nízké napětí baterie před uspáním (%.2fV)!\n", getBatteryVoltage());
    Serial.printf("Přecházím do dlouhodobého spánku na %d minut.\n", EMERGENCY_SLEEP_TIME);
    esp_sleep_enable_timer_wakeup(EMERGENCY_SLEEP_TIME * 60 * 1000000ULL);
  } else {
    // Kontrola, zda bylo provedeno úspěšné odeslání, pokud ne, zkusíme to za kratší dobu
    if (!sendSuccess && WiFi.status() == WL_CONNECTED) {
      // Pokud se nepodařilo odeslat data, ale WiFi je připojeno, možná dočasný problém se serverem
      // Zkusíme to znovu za kratší dobu (5 minut)
      Serial.println("Nepodařilo se odeslat data, zkusíme to znovu za 5 minut");
      esp_sleep_enable_timer_wakeup(5 * 60 * 1000000ULL);
    } else if (!sendSuccess) {
      // Pokud WiFi není připojeno, zkusíme to za 10 minut
      Serial.println("Nepodařilo se odeslat data (WiFi není připojeno), zkusíme to znovu za 10 minut");
      esp_sleep_enable_timer_wakeup(10 * 60 * 1000000ULL);
    } else {
      // Normální uspání na nastavenou dobu
      esp_sleep_enable_timer_wakeup(sleepTime * 60 * 1000000ULL);
    }
  }

    // Nastavení tlačítka pro probuzení z deep sleep
    // Pro ESP32-C3 musíme použít esp_deep_sleep_enable_gpio_wakeup místo esp_sleep_enable_ext0_wakeup
    // Nastavíme pin 2 jako vstup s pull-up rezistorem
    pinMode(TRIGGER_PIN, INPUT_PULLUP);

    // Nastavíme probuzení při LOW úrovni (když je tlačítko stisknuto)
    // 1ULL << TRIGGER_PIN vytvoří bitovou masku pro pin 2
    esp_deep_sleep_enable_gpio_wakeup(1ULL << TRIGGER_PIN, ESP_GPIO_WAKEUP_GPIO_LOW);

    // Přechod do hlubokého spánku
    esp_deep_sleep_start();
}
